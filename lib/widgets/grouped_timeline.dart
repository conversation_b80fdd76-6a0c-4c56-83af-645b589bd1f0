import 'package:flutter/material.dart';
import '../models/video_project.dart';
import '../utils/constants.dart';

class GroupedTimeline extends StatefulWidget {
  final double duration;
  final double currentPosition;
  final List<MediaItem> mediaItems;
  final Function(double) onPositionChanged;
  final Function(MediaItem) onMediaItemTap;
  final Function(MediaItem) onMediaItemDelete;
  final Function(MediaItem, double, double)? onMediaItemResize;
  final Function(MediaItem, double)? onMediaItemMove;
  final Function(MediaItem, double, double)? onMediaItemCut;
  final Function(MediaItem, double, double)? onMediaItemTrim;

  const GroupedTimeline({
    super.key,
    required this.duration,
    required this.currentPosition,
    required this.mediaItems,
    required this.onPositionChanged,
    required this.onMediaItemTap,
    required this.onMediaItemDelete,
    this.onMediaItemResize,
    this.onMediaItemMove,
    this.onMediaItemCut,
    this.onMediaItemTrim,
  });

  @override
  State<GroupedTimeline> createState() => _GroupedTimelineState();
}

class _GroupedTimelineState extends State<GroupedTimeline> {
  bool _isDragging = false;
  double _dragPosition = 0;

  // Zoom state
  double _zoomLevel = 0.5; // 0.5 = 50% (50px per second), 1.0 = 100% (100px per second)
  final double _minZoom = 0.01; // 1% (1px per second)
  final double _maxZoom = 1.0; // 100% (100px per second)

  // Cut/Trim state
  bool _isCutMode = false;
  bool _isTrimMode = false;
  double? _cutStartTime;
  double? _cutEndTime;
  MediaItem? _selectedItemForCut;

  // Drag and resize state
  MediaItem? _draggingItem;
  bool _isDraggingItem = false;
  bool _isResizingStart = false;
  bool _isResizingEnd = false;
  double _dragStartX = 0;
  double _originalStartTime = 0;
  double _originalEndTime = 0;

  // Selection state
  String? _selectedItemId;

  @override
  Widget build(BuildContext context) {
    // Group media items by type
    final groupedItems = _groupItemsByType();

    return Focus(
      autofocus: true,
      onKeyEvent: (node, event) {
        if (event.logicalKey.keyLabel == 'Delete' && _selectedItemId != null) {
          final selectedItem = widget.mediaItems.firstWhere((item) => item.id == _selectedItemId);
          widget.onMediaItemDelete(selectedItem);
          setState(() {
            _selectedItemId = null;
          });
          return KeyEventResult.handled;
        }
        return KeyEventResult.ignored;
      },
      child: _buildTimelineContent(groupedItems),
    );
  }

  Widget _buildTimelineContent(Map<MediaType, List<MediaItem>> groupedItems) {

    return Container(
      height: 280, // Increased height for zoom and cut/trim controls
      color: Color(AppConstants.surfaceColor),
      child: Column(
        children: [
          // Zoom controls
          Container(
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            child: Row(
              children: [
                Text(
                  'Zoom (1%-100%):',
                  style: TextStyle(color: Colors.white70, fontSize: 12),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: _zoomOut,
                  icon: const Icon(Icons.zoom_out, color: Colors.white70, size: 20),
                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                ),
                Expanded(
                  child: Slider(
                    value: _zoomLevel,
                    min: _minZoom,
                    max: _maxZoom,
                    divisions: 99, // 1% to 100% = 99 divisions
                    activeColor: Color(AppConstants.accentColor),
                    inactiveColor: Colors.white30,
                    onChanged: (value) {
                      setState(() {
                        _zoomLevel = value;
                      });
                    },
                  ),
                ),
                IconButton(
                  onPressed: _zoomIn,
                  icon: const Icon(Icons.zoom_in, color: Colors.white70, size: 20),
                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                ),
                Text(
                  '${(_zoomLevel * 100).round()}%',
                  style: TextStyle(color: Colors.white70, fontSize: 12),
                ),
              ],
            ),
          ),
          // Cut/Trim controls
          Container(
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  Text(
                    'Edit Tools:',
                    style: TextStyle(color: Colors.white70, fontSize: 12),
                  ),
                  const SizedBox(width: 16),
                  _buildToolButton(
                    icon: Icons.content_cut,
                    label: 'Cut',
                    isActive: _isCutMode,
                    onPressed: _toggleCutMode,
                  ),
                  const SizedBox(width: 8),
                  _buildToolButton(
                    icon: Icons.crop,
                    label: 'Trim',
                    isActive: _isTrimMode,
                    onPressed: _toggleTrimMode,
                  ),
                  if (_isCutMode || _isTrimMode) ...[
                    const SizedBox(width: 16),
                    Container(
                      constraints: const BoxConstraints(maxWidth: 200),
                      child: Text(
                        _isCutMode ? 'Click timeline to set cut points' : 'Click timeline to set trim range',
                        style: TextStyle(color: Colors.orange, fontSize: 11),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 16),
                    TextButton(
                      onPressed: _applyCutTrim,
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                        minimumSize: const Size(60, 32),
                      ),
                      child: Text(
                        'Apply',
                        style: TextStyle(color: Color(AppConstants.accentColor), fontSize: 12),
                      ),
                    ),
                    TextButton(
                      onPressed: _cancelCutTrim,
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                        minimumSize: const Size(60, 32),
                      ),
                      child: Text(
                        'Cancel',
                        style: TextStyle(color: Colors.white70, fontSize: 12),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          // Time ruler
          Container(
            height: 30,
            child: CustomPaint(
              painter: TimeRulerPainter(
                duration: widget.duration,
                currentPosition:
                    _isDragging ? _dragPosition : widget.currentPosition,
                zoomLevel: _zoomLevel,
              ),
              size: Size.infinite,
            ),
          ),
          // Grouped tracks
          Expanded(
            child: Row(
              children: [
                // Track labels
                Container(
                  width: 80,
                  child: Column(
                    children: [
                      _buildTrackLabel('Video', Colors.blue, Icons.videocam),
                      _buildTrackLabel('Audio', Colors.green, Icons.audiotrack),
                      _buildTrackLabel('Image', Colors.orange, Icons.image),
                      _buildTrackLabel(
                          'Text', Colors.purple, Icons.text_fields),
                    ],
                  ),
                ),
                // Timeline content with horizontal scroll
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: SizedBox(
                      width: _getTimelineWidth(),
                      child: GestureDetector(
                        onTapDown: (details) => _onTimelineClick(details),
                        child: Stack(
                          children: [
                            // Background grid
                            CustomPaint(
                              painter: GroupedGridPainter(
                                duration: widget.duration,
                                zoomLevel: _zoomLevel,
                              ),
                              size: Size(_getTimelineWidth(), 170),
                            ),
                            // Media items by type
                            ...groupedItems.entries.expand((entry) {
                              final type = entry.key;
                              final items = entry.value;
                              return items.map(
                                  (item) => _buildMediaItemWidget(item, type));
                            }),
                            // Transition indicators
                            ..._buildTransitionIndicators(),
                            // Cut/Trim indicators
                            if (_isCutMode || _isTrimMode) ..._buildCutTrimIndicators(),
                            // Playhead
                            Positioned(
                              left: _getPositionX(_isDragging
                                  ? _dragPosition
                                  : widget.currentPosition),
                              top: 0,
                              bottom: 0,
                              child: Container(
                                width: 2,
                                color: Color(AppConstants.accentColor),
                                child: Transform.translate(
                                  offset: const Offset(-3, 5),
                                  child: Container(
                                    width: 8,
                                    height: 8,
                                    decoration: BoxDecoration(
                                      color: Color(AppConstants.accentColor),
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Map<MediaType, List<MediaItem>> _groupItemsByType() {
    final grouped = <MediaType, List<MediaItem>>{
      MediaType.video: [],
      MediaType.audio: [],
      MediaType.image: [],
      MediaType.text: [],
    };

    for (final item in widget.mediaItems) {
      grouped[item.type]?.add(item);
    }

    // Sort items within each group by layer
    for (final items in grouped.values) {
      items.sort((a, b) => a.layer.compareTo(b.layer));
    }

    return grouped;
  }

  Widget _buildTrackLabel(String label, Color color, IconData icon) {
    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          color: Color(AppConstants.backgroundColor),
          border: Border(
            bottom: BorderSide(color: Colors.white10, width: 0.5),
            right: BorderSide(color: Colors.white10, width: 0.5),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 16),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaItemWidget(MediaItem item, MediaType type) {
    final left = _getPositionX(item.startTime);
    final width = _getPositionX(item.endTime) - left;
    final trackY = _getTrackY(type);
    final isSelected = _selectedItemId == item.id;
    final isDragging = _isDraggingItem && _draggingItem?.id == item.id;

    Color itemColor;
    switch (type) {
      case MediaType.video:
        itemColor = Colors.blue;
        break;
      case MediaType.audio:
        itemColor = Colors.green;
        break;
      case MediaType.image:
        itemColor = Colors.orange;
        break;
      case MediaType.text:
        itemColor = Colors.purple;
        break;
    }

    return Positioned(
      left: left,
      top: trackY,
      width: width.clamp(20.0, double.infinity), // Minimum width
      height: 35, // Track height
      child: Stack(
        children: [
          // Main item container
          MouseRegion(
            cursor: isSelected ? SystemMouseCursors.move : SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedItemId = _selectedItemId == item.id ? null : item.id;
                });
                widget.onMediaItemTap(item);
              },
              onLongPress: () => _showMediaItemMenu(item),
              onPanStart: (details) => _onItemPanStart(item, details),
              onPanUpdate: (details) => _onItemPanUpdate(item, details),
              onPanEnd: (details) => _onItemPanEnd(item),
              child: Container(
              width: width,
              height: 25,
              margin: const EdgeInsets.symmetric(vertical: 5),
              decoration: BoxDecoration(
                color: isDragging
                    ? itemColor.withValues(alpha: 0.6)
                    : isSelected
                        ? itemColor.withValues(alpha: 1.0)
                        : itemColor.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: isDragging
                      ? Colors.white
                      : isSelected ? Color(AppConstants.accentColor) : itemColor,
                  width: isDragging ? 3 : isSelected ? 2 : 1,
                ),
                boxShadow: isDragging ? [
                  BoxShadow(
                    color: Colors.white.withValues(alpha: 0.8),
                    blurRadius: 8,
                    spreadRadius: 2,
                  ),
                ] : isSelected ? [
                  BoxShadow(
                    color: Color(AppConstants.accentColor).withValues(alpha: 0.5),
                    blurRadius: 4,
                    spreadRadius: 1,
                  ),
                ] : null,
              ),
              child: Row(
                children: [
                  // Layer indicator
                  Container(
                    width: 20,
                    height: 20,
                    margin: const EdgeInsets.only(left: 2, right: 4),
                    decoration: BoxDecoration(
                      color: itemColor,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Center(
                      child: Text(
                        '${item.layer}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 8,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  if (width > 60)
                    Expanded(
                      child: Text(
                        _getItemDisplayText(item),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                ],
              ),
            ),
            ),
          ),

          // Left resize handle
          Positioned(
            left: 0,
            top: 5,
            bottom: 5,
            child: GestureDetector(
              onPanStart: (details) => _onResizeStart(item, true, details),
              onPanUpdate: (details) => _onResizeUpdate(item, true, details),
              onPanEnd: (details) => _onResizeEnd(item),
              child: Container(
                width: 6,
                decoration: BoxDecoration(
                  color: itemColor,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(4),
                    bottomLeft: Radius.circular(4),
                  ),
                ),
              ),
            ),
          ),

          // Right resize handle
          Positioned(
            right: 0,
            top: 5,
            bottom: 5,
            child: GestureDetector(
              onPanStart: (details) => _onResizeStart(item, false, details),
              onPanUpdate: (details) => _onResizeUpdate(item, false, details),
              onPanEnd: (details) => _onResizeEnd(item),
              child: Container(
                width: 6,
                decoration: BoxDecoration(
                  color: itemColor,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(4),
                    bottomRight: Radius.circular(4),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  double _getTrackY(MediaType type) {
    switch (type) {
      case MediaType.video:
        return 5;
      case MediaType.audio:
        return 45;
      case MediaType.image:
        return 85;
      case MediaType.text:
        return 125;
    }
  }

  double _getTimelineWidth() {
    // Calculate timeline width based on duration and zoom level
    // Base: 100px per second at 100% zoom (1.0), scaled by zoom level
    // At 1% zoom (0.01): 1px per second
    // At 100% zoom (1.0): 100px per second
    final basePixelsPerSecond = 100.0;
    final pixelsPerSecond = basePixelsPerSecond * _zoomLevel;
    final calculatedWidth = widget.duration * pixelsPerSecond;

    // Minimum width should also scale with zoom
    final minWidth = 500.0 * _zoomLevel;

    return calculatedWidth > minWidth ? calculatedWidth : minWidth;
  }

  void _zoomIn() {
    setState(() {
      // Increase by 10% (0.1) or multiply by 1.5, whichever is smaller
      final increment = (_zoomLevel * 0.5).clamp(0.05, 0.1);
      _zoomLevel = (_zoomLevel + increment).clamp(_minZoom, _maxZoom);
    });
  }

  void _zoomOut() {
    setState(() {
      // Decrease by 10% (0.1) or divide by 1.5, whichever is smaller
      final decrement = (_zoomLevel * 0.3).clamp(0.05, 0.1);
      _zoomLevel = (_zoomLevel - decrement).clamp(_minZoom, _maxZoom);
    });
  }

  // Cut/Trim methods
  void _toggleCutMode() {
    setState(() {
      _isCutMode = !_isCutMode;
      if (_isCutMode) {
        _isTrimMode = false;
        _cutStartTime = null;
        _cutEndTime = null;
        _selectedItemForCut = null;
      }
    });
  }

  void _toggleTrimMode() {
    setState(() {
      _isTrimMode = !_isTrimMode;
      if (_isTrimMode) {
        _isCutMode = false;
        _cutStartTime = null;
        _cutEndTime = null;
        _selectedItemForCut = null;
      }
    });
  }

  void _applyCutTrim() {
    if (_selectedItemForCut != null && _cutStartTime != null && _cutEndTime != null) {
      if (_isCutMode) {
        // Apply cut to selected item
        widget.onMediaItemCut?.call(_selectedItemForCut!, _cutStartTime!, _cutEndTime!);
      } else if (_isTrimMode) {
        // Apply trim to selected item
        widget.onMediaItemTrim?.call(_selectedItemForCut!, _cutStartTime!, _cutEndTime!);
      }
    }
    _cancelCutTrim();
  }

  void _cancelCutTrim() {
    setState(() {
      _isCutMode = false;
      _isTrimMode = false;
      _cutStartTime = null;
      _cutEndTime = null;
      _selectedItemForCut = null;
    });
  }

  Widget _buildToolButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onPressed,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isActive
              ? Color(AppConstants.accentColor).withValues(alpha: 0.3)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isActive
                ? Color(AppConstants.accentColor)
                : Colors.white30,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isActive
                  ? Color(AppConstants.accentColor)
                  : Colors.white70,
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: isActive
                    ? Color(AppConstants.accentColor)
                    : Colors.white70,
                fontSize: 12,
                fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  double _getPositionX(double time) {
    // Calculate position based on zoom level
    // At 100% zoom (1.0): 100px per second
    // At 1% zoom (0.01): 1px per second
    final pixelsPerSecond = 100.0 * _zoomLevel;
    return time * pixelsPerSecond;
  }

  String _getItemDisplayText(MediaItem item) {
    switch (item.type) {
      case MediaType.text:
        return item.text ?? 'Text';
      case MediaType.video:
        return 'Video ${item.layer}';
      case MediaType.audio:
        return 'Audio ${item.layer}';
      case MediaType.image:
        return 'Image ${item.layer}';
    }
  }

  void _showMediaItemMenu(MediaItem item) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Edit'),
              onTap: () {
                Navigator.pop(context);
                widget.onMediaItemTap(item);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Delete', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                widget.onMediaItemDelete(item);
              },
            ),
          ],
        ),
      ),
    );
  }

  // Drag and resize methods
  void _onItemPanStart(MediaItem item, DragStartDetails details) {
    // Only allow dragging if item is selected
    if (_selectedItemId != item.id) {
      setState(() {
        _selectedItemId = item.id;
      });
      return;
    }

    setState(() {
      _draggingItem = item;
      _isDraggingItem = true;
      _dragStartX = details.localPosition.dx;
      _originalStartTime = item.startTime;
      _originalEndTime = item.endTime;
    });
  }

  void _onItemPanUpdate(MediaItem item, DragUpdateDetails details) {
    if (!_isDraggingItem || _draggingItem?.id != item.id) return;

    final deltaX = details.localPosition.dx - _dragStartX;
    final deltaTime = _getTimeFromDeltaX(deltaX);

    var newStartTime = (_originalStartTime + deltaTime).clamp(0.0, widget.duration);

    // Snap to grid (0.1 second intervals)
    final snapInterval = 0.1;
    newStartTime = (newStartTime / snapInterval).round() * snapInterval;

    // Ensure the item doesn't go beyond timeline bounds
    final itemDuration = _originalEndTime - _originalStartTime;
    newStartTime = newStartTime.clamp(0.0, widget.duration - itemDuration);

    if (widget.onMediaItemMove != null) {
      widget.onMediaItemMove!(item, newStartTime);
    }
  }

  void _onItemPanEnd(MediaItem item) {
    setState(() {
      _draggingItem = null;
      _isDraggingItem = false;
    });
  }

  void _onResizeStart(
      MediaItem item, bool isLeftHandle, DragStartDetails details) {
    setState(() {
      _draggingItem = item;
      _isResizingStart = isLeftHandle;
      _isResizingEnd = !isLeftHandle;
      _dragStartX = details.localPosition.dx;
      _originalStartTime = item.startTime;
      _originalEndTime = item.endTime;
    });
  }

  void _onResizeUpdate(
      MediaItem item, bool isLeftHandle, DragUpdateDetails details) {
    if (_draggingItem?.id != item.id) return;

    final deltaX = details.localPosition.dx - _dragStartX;
    final deltaTime = _getTimeFromDeltaX(deltaX);

    double newStartTime = _originalStartTime;
    double newEndTime = _originalEndTime;

    if (isLeftHandle) {
      newStartTime =
          (_originalStartTime + deltaTime).clamp(0.0, _originalEndTime - 0.1);
    } else {
      newEndTime = (_originalEndTime + deltaTime)
          .clamp(_originalStartTime + 0.1, widget.duration);
    }

    if (widget.onMediaItemResize != null) {
      widget.onMediaItemResize!(item, newStartTime, newEndTime);
    }
  }

  void _onResizeEnd(MediaItem item) {
    setState(() {
      _draggingItem = null;
      _isResizingStart = false;
      _isResizingEnd = false;
    });
  }

  double _getTimeFromDeltaX(double deltaX) {
    // Calculate time from pixel delta based on zoom level
    final pixelsPerSecond = 100.0 * _zoomLevel;
    if (pixelsPerSecond <= 0) return 0.0;
    return deltaX / pixelsPerSecond;
  }

  void _onTimelineClick(TapDownDetails details) {
    final pixelsPerSecond = 100.0 * _zoomLevel;
    if (pixelsPerSecond <= 0) return;

    final clickX = details.localPosition.dx;
    final clickTime = clickX / pixelsPerSecond;
    final clampedTime = clickTime.clamp(0.0, widget.duration);

    // Handle cut/trim mode
    if (_isCutMode || _isTrimMode) {
      _handleCutTrimClick(clampedTime, details.localPosition.dy);
      return;
    }

    widget.onPositionChanged(clampedTime);
  }

  void _handleCutTrimClick(double clickTime, double clickY) {
    // Find which media item was clicked based on Y position
    MediaItem? clickedItem;

    // Adjust clickY to account for the timeline offset (time ruler + controls)
    final adjustedClickY = clickY - 70; // Account for zoom controls + cut/trim controls + time ruler

    // Determine track based on Y position
    if (adjustedClickY >= 5 && adjustedClickY <= 40) {
      // Video track
      clickedItem = _findItemAtTime(MediaType.video, clickTime);
    } else if (adjustedClickY >= 45 && adjustedClickY <= 80) {
      // Audio track
      clickedItem = _findItemAtTime(MediaType.audio, clickTime);
    } else if (adjustedClickY >= 85 && adjustedClickY <= 120) {
      // Image track
      clickedItem = _findItemAtTime(MediaType.image, clickTime);
    } else if (adjustedClickY >= 125 && adjustedClickY <= 160) {
      // Text track
      clickedItem = _findItemAtTime(MediaType.text, clickTime);
    }

    // If no specific item clicked, find any item at this time
    if (clickedItem == null) {
      // Find any media item at this time position
      final allItems = widget.mediaItems
          .where((item) => clickTime >= item.startTime && clickTime <= item.endTime)
          .toList();

      if (allItems.isNotEmpty) {
        // Sort by layer to get the topmost item
        allItems.sort((a, b) => b.layer.compareTo(a.layer));
        clickedItem = allItems.first;
      }
    }

    setState(() {
      if (clickedItem != null) {
        _selectedItemForCut = clickedItem;
      }

      if (_cutStartTime == null) {
        _cutStartTime = clickTime;
      } else if (_cutEndTime == null) {
        _cutEndTime = clickTime;
        // Ensure start is before end
        if (_cutStartTime! > _cutEndTime!) {
          final temp = _cutStartTime;
          _cutStartTime = _cutEndTime;
          _cutEndTime = temp;
        }
      } else {
        // Reset and start new selection
        _cutStartTime = clickTime;
        _cutEndTime = null;
      }
    });
  }

  MediaItem? _findItemAtTime(MediaType type, double time) {
    final items = widget.mediaItems
        .where((item) => item.type == type)
        .where((item) => time >= item.startTime && time <= item.endTime)
        .toList();

    if (items.isNotEmpty) {
      // Return the topmost item (highest layer)
      items.sort((a, b) => b.layer.compareTo(a.layer));
      return items.first;
    }

    return null;
  }

  List<Widget> _buildCutTrimIndicators() {
    final indicators = <Widget>[];
    final indicatorColor = _isCutMode ? Colors.red : Colors.orange;

    // Start time indicator
    if (_cutStartTime != null) {
      indicators.add(
        Positioned(
          left: _getPositionX(_cutStartTime!) - 1.5, // Center the line
          top: 0,
          bottom: 0,
          child: Container(
            width: 3,
            decoration: BoxDecoration(
              color: indicatorColor,
              boxShadow: [
                BoxShadow(
                  color: indicatorColor.withValues(alpha: 0.5),
                  blurRadius: 4,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Column(
              children: [
                Container(
                  width: 16,
                  height: 16,
                  margin: const EdgeInsets.only(bottom: 2),
                  decoration: BoxDecoration(
                    color: indicatorColor,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 1),
                  ),
                  child: Icon(
                    _isCutMode ? Icons.content_cut : Icons.crop_free,
                    color: Colors.white,
                    size: 10,
                  ),
                ),
                Expanded(
                  child: Container(
                    width: 3,
                    color: indicatorColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // End time indicator
    if (_cutEndTime != null) {
      indicators.add(
        Positioned(
          left: _getPositionX(_cutEndTime!) - 1.5, // Center the line
          top: 0,
          bottom: 0,
          child: Container(
            width: 3,
            decoration: BoxDecoration(
              color: indicatorColor,
              boxShadow: [
                BoxShadow(
                  color: indicatorColor.withValues(alpha: 0.5),
                  blurRadius: 4,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Column(
              children: [
                Container(
                  width: 16,
                  height: 16,
                  margin: const EdgeInsets.only(bottom: 2),
                  decoration: BoxDecoration(
                    color: indicatorColor,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 1),
                  ),
                  child: Icon(
                    _isCutMode ? Icons.content_cut : Icons.crop_free,
                    color: Colors.white,
                    size: 10,
                  ),
                ),
                Expanded(
                  child: Container(
                    width: 3,
                    color: indicatorColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // Selection area highlight
    if (_cutStartTime != null && _cutEndTime != null) {
      final startX = _getPositionX(_cutStartTime!);
      final endX = _getPositionX(_cutEndTime!);
      final width = (endX - startX).abs();
      final leftX = startX < endX ? startX : endX;

      indicators.add(
        Positioned(
          left: leftX,
          top: 0,
          width: width,
          height: 170,
          child: Container(
            decoration: BoxDecoration(
              color: indicatorColor.withValues(alpha: 0.15),
              border: Border.all(
                color: indicatorColor,
                width: 2,
                style: BorderStyle.solid,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: indicatorColor.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _isCutMode ? 'CUT SELECTION' : 'TRIM SELECTION',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          ),
        ),
      );
    }

    // Show selected item highlight
    if (_selectedItemForCut != null) {
      final item = _selectedItemForCut!;
      final startX = _getPositionX(item.startTime);
      final endX = _getPositionX(item.endTime);
      final width = endX - startX;
      final trackY = _getTrackY(item.type);

      indicators.add(
        Positioned(
          left: startX,
          top: trackY - 2,
          width: width,
          height: 39, // Track height + margin
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: indicatorColor,
                width: 3,
              ),
              borderRadius: BorderRadius.circular(6),
            ),
          ),
        ),
      );
    }

    return indicators;
  }

  List<Widget> _buildTransitionIndicators() {
    final indicators = <Widget>[];

    // Individual item transitions
    for (final item in widget.mediaItems) {
      if (item.transitionSettings != null &&
          item.transitionSettings!.type != TransitionType.none) {

        final transition = item.transitionSettings!;
        final itemStartX = _getPositionX(item.startTime);
        final itemEndX = _getPositionX(item.endTime);
        final itemWidth = itemEndX - itemStartX;
        final trackY = _getTrackY(item.type);

        // Transition duration indicator
        final transitionWidth = _getPositionX(transition.duration);

        // Show transition at the beginning of the item
        indicators.add(
          Positioned(
            left: itemStartX,
            top: trackY - 3,
            width: transitionWidth.clamp(10.0, itemWidth),
            height: 6,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.purple.withValues(alpha: 0.8),
                    Colors.purple.withValues(alpha: 0.3),
                  ],
                ),
                borderRadius: BorderRadius.circular(3),
                border: Border.all(color: Colors.purple, width: 0.5),
              ),
              child: Center(
                child: Icon(
                  _getTransitionIcon(transition.type),
                  color: Colors.white,
                  size: 4,
                ),
              ),
            ),
          ),
        );

        // Transition label (only if item is wide enough)
        if (itemWidth > 80) {
          indicators.add(
            Positioned(
              left: itemStartX + 2,
              top: trackY + 28,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                decoration: BoxDecoration(
                  color: Colors.purple.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _getTransitionName(transition.type),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 8,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          );
        }
      }
    }

    // Video-to-video transitions
    indicators.addAll(_buildVideoToVideoTransitions());

    return indicators;
  }

  List<Widget> _buildVideoToVideoTransitions() {
    final indicators = <Widget>[];

    // Get all video items sorted by start time
    final videoItems = widget.mediaItems
        .where((item) => item.type == MediaType.video)
        .toList()
      ..sort((a, b) => a.startTime.compareTo(b.startTime));

    // Find consecutive videos and show transitions between them
    for (int i = 0; i < videoItems.length - 1; i++) {
      final currentVideo = videoItems[i];
      final nextVideo = videoItems[i + 1];

      // Check if videos are consecutive (next video starts when current ends)
      final gap = nextVideo.startTime - currentVideo.endTime;
      if (gap.abs() < 0.1) { // Allow small gap tolerance

        // Determine which transition to show
        TransitionSettings? transitionToShow;
        if (nextVideo.transitionSettings != null && nextVideo.transitionSettings!.type != TransitionType.none) {
          transitionToShow = nextVideo.transitionSettings;
        } else if (currentVideo.transitionSettings != null && currentVideo.transitionSettings!.type != TransitionType.none) {
          transitionToShow = currentVideo.transitionSettings;
        }

        if (transitionToShow != null) {
          final transitionX = _getPositionX(currentVideo.endTime);
          final trackY = _getTrackY(MediaType.video);

          // Transition connector between videos
          indicators.add(
            Positioned(
              left: transitionX - 15,
              top: trackY + 10,
              width: 30,
              height: 15,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.cyan.withValues(alpha: 0.8),
                      Colors.blue.withValues(alpha: 0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.cyan, width: 1),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.cyan.withValues(alpha: 0.3),
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _getTransitionIcon(transitionToShow.type),
                      color: Colors.white,
                      size: 8,
                    ),
                    const SizedBox(width: 2),
                    Icon(
                      Icons.arrow_forward,
                      color: Colors.white,
                      size: 6,
                    ),
                  ],
                ),
              ),
            ),
          );

          // Transition label
          indicators.add(
            Positioned(
              left: transitionX - 20,
              top: trackY - 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.cyan.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Colors.white, width: 0.5),
                ),
                child: Text(
                  '${_getTransitionName(transitionToShow.type)} ${transitionToShow.duration.toStringAsFixed(1)}s',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 7,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          );
        }
      }
    }

    return indicators;
  }

  IconData _getTransitionIcon(TransitionType type) {
    switch (type) {
      case TransitionType.none:
        return Icons.block;
      case TransitionType.fade:
        return Icons.gradient;
      case TransitionType.slideLeft:
        return Icons.keyboard_arrow_left;
      case TransitionType.slideRight:
        return Icons.keyboard_arrow_right;
      case TransitionType.slideUp:
        return Icons.keyboard_arrow_up;
      case TransitionType.slideDown:
        return Icons.keyboard_arrow_down;
      case TransitionType.zoom:
        return Icons.zoom_in;
      case TransitionType.dissolve:
        return Icons.blur_on;
      case TransitionType.wipe:
        return Icons.swipe;
      case TransitionType.circle:
        return Icons.circle;
    }
  }

  String _getTransitionName(TransitionType type) {
    switch (type) {
      case TransitionType.none:
        return 'None';
      case TransitionType.fade:
        return 'Fade';
      case TransitionType.slideLeft:
        return 'Left';
      case TransitionType.slideRight:
        return 'Right';
      case TransitionType.slideUp:
        return 'Up';
      case TransitionType.slideDown:
        return 'Down';
      case TransitionType.zoom:
        return 'Zoom';
      case TransitionType.dissolve:
        return 'Dissolve';
      case TransitionType.wipe:
        return 'Wipe';
      case TransitionType.circle:
        return 'Circle';
    }
  }
}

class TimeRulerPainter extends CustomPainter {
  final double duration;
  final double currentPosition;
  final double zoomLevel;

  TimeRulerPainter({
    required this.duration,
    required this.currentPosition,
    this.zoomLevel = 1.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (duration <= 0 || size.width <= 0 || size.height <= 0) return;

    final paint = Paint()
      ..color = Colors.white70
      ..strokeWidth = 1;

    final textPainter = TextPainter(textDirection: TextDirection.ltr);

    // Calculate appropriate interval based on zoom level
    double interval;
    if (zoomLevel >= 0.5) {
      // High zoom: show every second
      interval = 1.0;
    } else if (zoomLevel >= 0.2) {
      // Medium zoom: show every 5 seconds
      interval = 5.0;
    } else if (zoomLevel >= 0.1) {
      // Low zoom: show every 10 seconds
      interval = 10.0;
    } else {
      // Very low zoom: show every 30 seconds
      interval = 30.0;
    }

    // Draw time markers
    final pixelsPerSecond = 100.0 * zoomLevel;
    final numMarkers = (duration / interval).ceil();

    for (int i = 0; i <= numMarkers; i++) {
      final time = i * interval;
      if (time > duration) break;

      final x = time * pixelsPerSecond;

      if (!x.isFinite) continue;

      // Draw tick
      canvas.drawLine(
        Offset(x, size.height - 10),
        Offset(x, size.height),
        paint,
      );

      // Draw time label (only if there's enough space)
      if (zoomLevel >= 0.1) {
        final timeText = _formatTime(time);
        textPainter.text = TextSpan(
          text: timeText,
          style: const TextStyle(color: Colors.white70, fontSize: 10),
        );
        textPainter.layout();

        final textX = x - textPainter.width / 2;
        if (textX.isFinite && textX >= 0 && textX + textPainter.width <= size.width) {
          textPainter.paint(canvas, Offset(textX, 0));
        }
      }
    }

    // Draw playhead
    final playheadX = currentPosition * pixelsPerSecond;
    if (playheadX.isFinite && playheadX >= 0 && playheadX <= size.width) {
      final playheadPaint = Paint()
        ..color = Colors.red
        ..strokeWidth = 2;

      canvas.drawLine(
        Offset(playheadX, 0),
        Offset(playheadX, size.height),
        playheadPaint,
      );
    }
  }

  String _formatTime(double seconds) {
    final minutes = (seconds / 60).floor();
    final secs = (seconds % 60).floor();
    return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class GroupedGridPainter extends CustomPainter {
  final double duration;
  final double zoomLevel;

  GroupedGridPainter({
    required this.duration,
    this.zoomLevel = 1.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (duration <= 0 || size.width <= 0 || size.height <= 0) return;

    final paint = Paint()
      ..color = Colors.white10
      ..strokeWidth = 0.5;

    // Draw horizontal lines for tracks
    for (int i = 1; i < 4; i++) {
      final y = i * (size.height / 4);
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // Draw vertical grid lines based on zoom level
    final pixelsPerSecond = 100.0 * zoomLevel;

    // Calculate appropriate grid interval based on zoom level
    double gridInterval;
    if (zoomLevel >= 0.5) {
      gridInterval = 1.0; // Every second
    } else if (zoomLevel >= 0.2) {
      gridInterval = 5.0; // Every 5 seconds
    } else if (zoomLevel >= 0.1) {
      gridInterval = 10.0; // Every 10 seconds
    } else {
      gridInterval = 30.0; // Every 30 seconds
    }

    final numGridLines = (duration / gridInterval).ceil();
    for (int i = 1; i <= numGridLines; i++) {
      final time = i * gridInterval;
      if (time > duration) break;

      final x = time * pixelsPerSecond;
      if (x.isFinite && x <= size.width) {
        canvas.drawLine(
          Offset(x, 0),
          Offset(x, size.height),
          paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
