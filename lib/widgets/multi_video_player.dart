import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'dart:io';
import '../models/video_project.dart';

class MultiVideoPlayer extends StatefulWidget {
  final List<MediaItem> videoItems;
  final double currentPosition;
  final double aspectRatio;

  const MultiVideoPlayer({
    super.key,
    required this.videoItems,
    required this.currentPosition,
    required this.aspectRatio,
  });

  @override
  State<MultiVideoPlayer> createState() => _MultiVideoPlayerState();
}

class _MultiVideoPlayerState extends State<MultiVideoPlayer> {
  Map<String, VideoPlayerController> _controllers = {};
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  @override
  void didUpdateWidget(MultiVideoPlayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.videoItems != widget.videoItems) {
      _disposeControllers();
      _initializeControllers();
    }
  }

  @override
  void dispose() {
    _disposeControllers();
    super.dispose();
  }

  void _disposeControllers() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    _controllers.clear();
  }

  Future<void> _initializeControllers() async {
    setState(() {
      _isInitialized = false;
    });

    final newControllers = <String, VideoPlayerController>{};

    // Initialize only the main video (layer 0) to avoid MediaCodec conflicts
    final mainVideo = widget.videoItems.isNotEmpty
        ? widget.videoItems.where((item) => item.layer == 0).firstOrNull
        : null;

    if (mainVideo?.filePath != null) {
      final controller = VideoPlayerController.file(File(mainVideo!.filePath!));
      newControllers[mainVideo.id] = controller;

      try {
        await controller.initialize();
        controller.setLooping(false);
        controller.setVolume(mainVideo.volume ?? 1.0);
      } catch (e) {
        print('Error initializing main video controller: $e');
      }
    }

    if (mounted) {
      setState(() {
        _controllers = newControllers;
        _isInitialized = true;
      });
      _syncVideoPositions();
    }
  }

  void _syncVideoPositions() {
    for (final entry in _controllers.entries) {
      try {
        final videoItem = widget.videoItems.firstWhere(
          (item) => item.id == entry.key,
        );

        if (widget.currentPosition >= videoItem.startTime &&
            widget.currentPosition <= videoItem.endTime) {
          final relativePosition = widget.currentPosition - videoItem.startTime;
          final duration = Duration(milliseconds: (relativePosition * 1000).round());
          entry.value.seekTo(duration);
        }
      } catch (e) {
        // Video item not found, skip
        continue;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized || widget.videoItems.isEmpty) {
      return AspectRatio(
        aspectRatio: widget.aspectRatio,
        child: Container(
          color: Colors.black,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    // Sort video items by layer (lower layers appear behind)
    final sortedVideoItems = List<MediaItem>.from(widget.videoItems)
      ..sort((a, b) => a.layer.compareTo(b.layer));

    return AspectRatio(
      aspectRatio: widget.aspectRatio,
      child: Stack(
        children: sortedVideoItems.map((videoItem) {
          // Check if video should be visible at current time
          if (widget.currentPosition < videoItem.startTime ||
              widget.currentPosition > videoItem.endTime) {
            return const SizedBox.shrink();
          }

          Widget videoWidget;
          final controller = _controllers[videoItem.id];

          if (videoItem.layer == 0 && controller != null && controller.value.isInitialized) {
            // Main video - use actual VideoPlayer
            videoWidget = VideoPlayer(controller);
          } else {
            // Overlay videos - use placeholder for now to avoid MediaCodec conflicts
            videoWidget = Container(
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue, width: 2),
              ),
              child: Stack(
                children: [
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.videocam,
                          color: Colors.white,
                          size: 40,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Video Layer ${videoItem.layer}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.blue,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'L${videoItem.layer}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          }

          // Apply transform if available
          if (videoItem.transformSettings != null) {
            videoWidget = _applyTransform(videoWidget, videoItem.transformSettings!);
          }

          // Apply opacity
          if (videoItem.opacity < 1.0) {
            videoWidget = Opacity(
              opacity: videoItem.opacity,
              child: videoWidget,
            );
          }

          // Position the video
          return Positioned(
            left: videoItem.x ?? 0,
            top: videoItem.y ?? 0,
            width: videoItem.width ?? 200,
            height: videoItem.height ?? 150,
            child: videoWidget,
          );
        }).toList(),
      ),
    );
  }

  Widget _applyTransform(Widget child, TransformSettings transform) {
    return Transform(
      alignment: Alignment.center,
      transform: Matrix4.identity()
        ..translate(transform.translateX, transform.translateY)
        ..scale(transform.scaleX, transform.scaleY)
        ..rotateZ(transform.rotation * (3.14159 / 180)),
      child: child,
    );
  }

  // Method to sync all video controllers to a specific position
  void seekTo(double position) {
    for (final entry in _controllers.entries) {
      final videoItem = widget.videoItems.firstWhere(
        (item) => item.id == entry.key,
        orElse: () => widget.videoItems.first,
      );

      if (position >= videoItem.startTime && position <= videoItem.endTime) {
        final relativePosition = position - videoItem.startTime;
        final duration = Duration(milliseconds: (relativePosition * 1000).round());
        entry.value.seekTo(duration);
      }
    }
  }

  // Method to play/pause all video controllers
  void setPlaying(bool playing) {
    for (final entry in _controllers.entries) {
      final videoItem = widget.videoItems.firstWhere(
        (item) => item.id == entry.key,
        orElse: () => widget.videoItems.first,
      );

      if (widget.currentPosition >= videoItem.startTime &&
          widget.currentPosition <= videoItem.endTime) {
        if (playing) {
          entry.value.play();
        } else {
          entry.value.pause();
        }
      }
    }
  }
}
